#!/usr/bin/env python
"""
快速测试脚本 - 验证整个系统是否正常工作
"""

import requests
import json
import time

def test_lightrag_server():
    """测试LightRAG服务器"""
    print("🚀 测试LightRAG服务器...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:9621/health", timeout=10)
        if response.status_code == 200:
            print("✅ LightRAG服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到LightRAG服务器")
        print("💡 请先启动服务器: lightrag-server")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def test_document_upload():
    """测试文档上传"""
    print("\n📄 测试文档上传...")
    
    # 创建测试文档
    test_content = """
    无人机目标识别系统技术文档
    
    1. 系统概述
    无人机目标识别系统是一个基于多模态数据融合的智能识别平台，
    能够处理雷达、图像、视频和频谱等多种数据源。
    
    2. 核心技术
    - 雷达信号处理：采用X波段雷达，距离分辨率0.5米
    - 图像识别：基于深度学习的目标检测算法
    - 频谱分析：支持2.4GHz和5GHz频段监测
    - 威胁评估：多维度威胁等级评估模型
    
    3. 系统特点
    - 实时处理能力
    - 多模态数据融合
    - 智能威胁评估
    - 可视化态势展示
    """
    
    try:
        # 上传文档内容
        response = requests.post(
            "http://localhost:9621/documents/text",
            json={"content": test_content, "description": "无人机系统测试文档"},
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ 文档上传成功")
            return True
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ 文档上传错误: {e}")
        return False

def test_query():
    """测试查询功能"""
    print("\n🔍 测试查询功能...")
    
    queries = [
        "无人机目标识别系统有什么特点？",
        "系统支持哪些数据源？",
        "雷达的技术参数是什么？"
    ]
    
    for query in queries:
        try:
            print(f"\n📝 查询: {query}")
            
            response = requests.post(
                "http://localhost:9621/query",
                json={"query": query, "mode": "hybrid"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get("answer", "无回答")
                print(f"✅ 回答: {answer[:100]}...")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 查询错误: {e}")

def test_knowledge_graph():
    """测试知识图谱"""
    print("\n🕸️ 测试知识图谱...")
    
    try:
        response = requests.get("http://localhost:9621/graph/stats", timeout=30)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 知识图谱统计:")
            print(f"   节点数: {stats.get('nodes', 0)}")
            print(f"   边数: {stats.get('edges', 0)}")
            return True
        else:
            print(f"❌ 知识图谱获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 知识图谱错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 无人机知识图谱系统快速测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("LightRAG服务器", test_lightrag_server),
        ("文档上传", test_document_upload),
        ("查询功能", test_query),
        ("知识图谱", test_knowledge_graph)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
            if not results[test_name]:
                print(f"\n⚠️ {test_name}测试失败，跳过后续测试")
                break
            time.sleep(2)  # 等待2秒
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results[test_name] = False
            break
    
    # 生成测试报告
    print("\n" + "=" * 50)
    print("📊 测试结果报告")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n成功率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常！")
        print("\n📋 你现在可以:")
        print("1. 访问Web界面: http://localhost:9621")
        print("2. 查看API文档: http://localhost:9621/docs")
        print("3. 运行无人机集成系统: python drone_integrated_system.py")
        print("4. 上传你的文档开始构建知识图谱")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
