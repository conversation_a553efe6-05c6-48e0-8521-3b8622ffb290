@echo off
echo 🚀 使用Python 3.12启动LightRAG服务器
echo ================================================

REM 设置Python 3.12路径
set PYTHON_PATH=E:\python3.12
set PATH=%PYTHON_PATH%;%PYTHON_PATH%\Scripts;%PATH%

REM 验证Python版本
echo 📍 当前Python版本:
python --version

REM 检查LightRAG是否安装
echo.
echo 🔍 检查LightRAG安装状态...
python -c "import lightrag; print('✅ LightRAG已安装')" 2>nul || (
    echo ❌ LightRAG未安装，正在安装...
    pip install "lightrag-hku[api]"
)

REM 启动服务器
echo.
echo 🚀 启动LightRAG服务器...
echo 📍 访问地址: http://localhost:9621
echo 按 Ctrl+C 停止服务器
echo.

lightrag-server

pause
