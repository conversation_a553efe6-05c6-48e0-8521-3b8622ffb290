# 🧠 DeepSeek模型配置指南

## 🎯 DeepSeek模型选择

### 推荐模型版本：

1. **deepseek-r1:7b** ⭐ (推荐)
   - 内存需求：约8GB
   - 速度：快
   - 适合：开发测试、资源有限的环境

2. **deepseek-r1:14b** 🚀 (高性能)
   - 内存需求：约16GB
   - 速度：中等
   - 适合：生产环境、对效果要求高的场景

3. **deepseek-coder:6.7b** 💻 (代码专用)
   - 内存需求：约8GB
   - 特点：专门针对代码理解和生成优化
   - 适合：技术文档处理、代码分析

## 🔧 完整配置步骤

### 1. 下载模型
```bash
# 下载DeepSeek模型
ollama pull deepseek-r1:7b

# 下载embedding模型
ollama pull bge-m3:latest

# 验证模型下载
ollama list
```

### 2. 配置文件设置
你的.env文件已经更新为：
```bash
LLM_BINDING=ollama
LLM_MODEL=deepseek-r1:7b
LLM_BINDING_HOST=http://localhost:11434
OLLAMA_LLM_NUM_CTX=32768

EMBEDDING_BINDING=ollama
EMBEDDING_BINDING_HOST=http://localhost:11434
EMBEDDING_MODEL=bge-m3:latest
EMBEDDING_DIM=1024
```

### 3. 针对DeepSeek的优化配置
```bash
# DeepSeek特定优化参数
TEMPERATURE=0.1              # 降低随机性，提高一致性
MAX_TOKENS=32768            # DeepSeek支持长上下文
TIMEOUT=300                 # 增加超时时间
MAX_ASYNC=2                 # 降低并发，避免内存不足
```

## 🚀 启动和测试

### 1. 启动Ollama服务
```bash
# 启动Ollama（如果没有自动启动）
ollama serve
```

### 2. 测试模型
```bash
# 测试DeepSeek模型
ollama run deepseek-r1:7b "你好，请介绍一下你自己"

# 测试embedding模型
ollama run bge-m3:latest
```

### 3. 启动LightRAG服务
```bash
lightrag-server
```

## 💡 DeepSeek模型特点

### 优势：
- 🇨🇳 **中文支持优秀**：对中文理解和生成能力强
- 🧠 **推理能力强**：在逻辑推理和分析方面表现出色
- 💻 **代码理解好**：特别适合技术文档处理
- 🆓 **完全开源**：可以本地部署，保护数据隐私
- ⚡ **效率高**：相比同等参数的模型，推理速度更快

### 适用场景：
- 📄 技术文档分析
- 🛡️ 军用/敏感数据处理（本地部署）
- 🔍 复杂逻辑推理
- 📊 数据分析和报告生成

## 🎯 针对无人机项目的优化

### 1. 专业术语配置
```bash
# 在.env中添加
SUMMARY_LANGUAGE=Chinese
ENTITY_TYPES=["aircraft", "radar", "spectrum", "target", "threat"]
```

### 2. 上下文窗口优化
```bash
# 针对雷达和频谱数据的长文本处理
OLLAMA_LLM_NUM_CTX=65536    # 增加到64K上下文
CHUNK_SIZE=1500             # 增加块大小
MAX_TOKEN_SUMMARY=800       # 增加摘要长度
```

### 3. 性能调优
```bash
# 根据你的硬件配置调整
MAX_ASYNC=2                 # 如果内存不足，降低并发
MAX_PARALLEL_INSERT=1       # 降低并行处理文档数
ENABLE_LLM_CACHE=true       # 启用缓存提高效率
```

## 🔧 故障排除

### 问题1：内存不足
```bash
# 使用更小的模型
ollama pull deepseek-r1:1.3b

# 或者调整配置
MAX_ASYNC=1
OLLAMA_LLM_NUM_CTX=16384
```

### 问题2：响应速度慢
```bash
# 优化配置
TEMPERATURE=0
MAX_TOKENS=16384
TIMEOUT=600
```

### 问题3：中文处理问题
```bash
# 确保语言设置
SUMMARY_LANGUAGE=Chinese
LC_ALL=zh_CN.UTF-8
```

## 📊 性能对比

| 模型 | 内存需求 | 速度 | 中文能力 | 推理能力 | 适用场景 |
|------|----------|------|----------|----------|----------|
| deepseek-r1:7b | 8GB | 快 | 优秀 | 很强 | 开发测试 |
| deepseek-r1:14b | 16GB | 中等 | 优秀 | 极强 | 生产环境 |
| deepseek-coder:6.7b | 8GB | 快 | 良好 | 强 | 代码分析 |

## 🎉 完成配置

现在你的系统已经配置为使用DeepSeek模型！

启动命令：
```bash
lightrag-server
```

访问Web界面：http://localhost:9621

开始使用你的无人机知识图谱系统吧！
