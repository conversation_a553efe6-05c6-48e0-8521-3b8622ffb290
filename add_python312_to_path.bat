@echo off
chcp 65001 >nul

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  此脚本需要管理员权限来修改系统环境变量
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 🔧 自动添加Python 3.12到系统PATH
echo ================================================

echo 📍 当前Python版本:
python --version 2>nul || echo ❌ Python命令不可用

echo.
echo 📍 当前PATH中的Python路径:
where python 2>nul || echo ❌ 未找到Python

echo.
echo 🔍 检查Python 3.12是否存在:
if exist "E:\python3.12\python.exe" (
    echo ✅ 找到Python 3.12: E:\python3.12\python.exe
    E:\python3.12\python.exe --version
) else (
    echo ❌ 未找到Python 3.12，请确认安装路径
    echo 请检查Python 3.12是否安装在: E:\python3.12\
    pause
    exit /b 1
)

echo.
echo 🔧 正在添加Python 3.12到系统PATH...

:: 获取当前系统PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "CURRENT_PATH=%%b"

:: 检查是否已经包含Python 3.12路径
echo %CURRENT_PATH% | findstr /i "E:\python3.12" >nul
if %errorlevel% equ 0 (
    echo ⚠️  Python 3.12路径已存在于PATH中
    echo 可能需要调整路径顺序，请手动检查
) else (
    :: 添加Python 3.12路径到PATH前面
    set "NEW_PATH=E:\python3.12;E:\python3.12\Scripts;%CURRENT_PATH%"
    
    :: 更新系统PATH
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%NEW_PATH%" /f >nul
    
    if %errorlevel% equ 0 (
        echo ✅ 成功添加Python 3.12到系统PATH
    ) else (
        echo ❌ 添加失败，请手动添加
    )
)

echo.
echo 📋 手动验证步骤:
echo 1. 完全关闭所有命令提示符窗口
echo 2. 重新打开命令提示符
echo 3. 运行: python --version
echo 4. 应该显示: Python 3.12.x
echo.

echo 🚀 如果验证成功，可以运行:
echo pip install "lightrag-hku[api]"
echo lightrag-server
echo.

echo ⚠️  注意: 环境变量更改需要重新打开命令提示符才能生效
pause
