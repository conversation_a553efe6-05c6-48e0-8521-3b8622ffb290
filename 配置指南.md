# 🚀 LightRAG + RAG-Anything 配置指南

## 📋 配置选择

你需要配置两个核心组件：
1. **LLM（大语言模型）** - 用于理解和生成文本
2. **Embedding模型** - 用于将文本转换为向量

## 🎯 推荐配置方案

### 方案一：完全本地化（推荐新手）✨

**优点**：免费、隐私保护、无需API密钥
**缺点**：需要较好的硬件配置，速度相对较慢

#### 1. 安装Ollama
```bash
# Windows: 下载安装包
# https://ollama.ai/download

# Linux/Mac:
curl -fsSL https://ollama.ai/install.sh | sh
```

#### 2. 下载模型
```bash
# 下载中文友好的LLM（约4GB）
ollama pull qwen2.5:7b

# 下载多语言embedding模型（约2GB）
ollama pull bge-m3:latest
```

#### 3. 配置.env文件
已经为你创建好了，使用默认配置即可！

### 方案二：使用OpenAI API（推荐生产环境）🚀

**优点**：性能最好、速度快、稳定
**缺点**：需要付费、需要网络连接

#### 1. 获取API密钥
- 访问 https://platform.openai.com/
- 注册账号并获取API密钥

#### 2. 修改.env文件
```bash
# 注释掉Ollama配置，启用OpenAI配置
LLM_BINDING=openai
LLM_MODEL=gpt-4o-mini
LLM_BINDING_HOST=https://api.openai.com/v1
LLM_BINDING_API_KEY=sk-your-openai-api-key

EMBEDDING_BINDING=openai
EMBEDDING_MODEL=text-embedding-3-large
EMBEDDING_DIM=3072

# 同时设置RAG-Anything的API密钥
OPENAI_API_KEY=sk-your-openai-api-key
```

### 方案三：使用国产API（推荐中国用户）🇨🇳

**优点**：国内访问快、价格便宜、中文支持好
**缺点**：需要付费

#### 支持的国产API：
- **智谱AI**：https://open.bigmodel.cn/
- **百度千帆**：https://cloud.baidu.com/
- **阿里云通义**：https://dashscope.aliyun.com/

#### 配置示例（智谱AI）：
```bash
LLM_BINDING=openai
LLM_MODEL=glm-4-flash
LLM_BINDING_HOST=https://open.bigmodel.cn/api/paas/v4
LLM_BINDING_API_KEY=your-zhipu-api-key

EMBEDDING_BINDING=openai
EMBEDDING_MODEL=embedding-2
EMBEDDING_DIM=1024
```

## 🚀 快速启动步骤

### 1. 选择配置方案
根据上面的说明选择一种配置方案

### 2. 启动服务
```bash
# 确保在项目根目录
cd RAG-Anything-main

# 启动LightRAG服务器
lightrag-server

# 或者运行你的集成系统
python drone_integrated_system.py
```

### 3. 验证配置
访问 http://localhost:9621 查看Web界面

## ⚠️ 重要注意事项

1. **嵌入模型不能随意更换**
   - 一旦开始索引文档，就不能更换embedding模型
   - 如需更换，必须删除所有数据重新开始

2. **硬件要求**
   - Ollama本地部署：至少8GB内存，推荐16GB
   - API方案：无特殊硬件要求

3. **网络要求**
   - 本地方案：无网络要求
   - API方案：需要稳定的网络连接

## 🔧 故障排除

### 问题1：Ollama连接失败
```bash
# 检查Ollama是否运行
ollama list

# 重启Ollama服务
ollama serve
```

### 问题2：API密钥错误
- 检查API密钥是否正确
- 确认账户余额是否充足
- 验证API访问权限

### 问题3：模型下载失败
```bash
# 手动下载模型
ollama pull qwen2.5:7b --verbose
```

## 💡 推荐配置

**对于你的无人机项目，我推荐：**

1. **开发测试阶段**：使用方案一（Ollama本地）
2. **生产部署阶段**：使用方案二（OpenAI API）或方案三（国产API）

这样既能在开发时节省成本，又能在生产时获得最佳性能！
