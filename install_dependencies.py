#!/usr/bin/env python
"""
一键安装LightRAG所有依赖的脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ {description} 出错: {e}")
        return False

def install_dependencies():
    """安装所有依赖"""
    print("🚀 开始安装LightRAG所有依赖")
    print("=" * 50)
    
    # 基础依赖列表
    dependencies = [
        "pipmaster",
        "python-dotenv", 
        "fastapi",
        "uvicorn[standard]",
        "gunicorn",
        "pydantic",
        "httpx",
        "aiofiles",
        "jinja2",
        "python-multipart",
        "requests"
    ]
    
    # 检查是否使用镜像源
    use_mirror = input("是否使用清华镜像源加速下载? (推荐中国用户) [y/N]: ").lower() == 'y'
    
    mirror_option = "-i https://pypi.tuna.tsinghua.edu.cn/simple" if use_mirror else ""
    
    # 逐个安装依赖
    failed_deps = []
    for dep in dependencies:
        command = f"pip install {mirror_option} {dep}"
        if not run_command(command, f"安装 {dep}"):
            failed_deps.append(dep)
    
    # 安装LightRAG
    print("\n🔧 安装LightRAG...")
    lightrag_command = f'pip install {mirror_option} "lightrag-hku[api]"'
    lightrag_success = run_command(lightrag_command, "安装 lightrag-hku[api]")
    
    # 报告结果
    print("\n" + "=" * 50)
    print("📊 安装结果报告")
    print("=" * 50)
    
    if failed_deps:
        print(f"❌ 失败的依赖: {', '.join(failed_deps)}")
        print("请手动安装失败的依赖")
    else:
        print("✅ 所有基础依赖安装成功")
    
    if lightrag_success:
        print("✅ LightRAG安装成功")
    else:
        print("❌ LightRAG安装失败")
    
    # 验证安装
    print("\n🔍 验证安装...")
    verification_modules = [
        ("lightrag", "LightRAG核心"),
        ("pipmaster", "PipMaster"),
        ("dotenv", "Python-dotenv"),
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic")
    ]
    
    all_verified = True
    for module, name in verification_modules:
        try:
            __import__(module)
            print(f"✅ {name} - 验证通过")
        except ImportError:
            print(f"❌ {name} - 验证失败")
            all_verified = False
    
    print("\n" + "=" * 50)
    if all_verified and lightrag_success:
        print("🎉 所有依赖安装完成！")
        print("\n📋 下一步操作:")
        print("1. 启动LightRAG服务器: lightrag-server")
        print("2. 或使用启动脚本: python start_lightrag_server.py")
        print("3. 或运行测试: python test_deepseek.py")
    else:
        print("⚠️ 部分依赖安装失败，请检查错误信息")
        
        # 提供手动安装命令
        print("\n🔧 手动安装命令:")
        if failed_deps:
            deps_str = " ".join(failed_deps)
            print(f"pip install {mirror_option} {deps_str}")
        
        if not lightrag_success:
            print(f'pip install {mirror_option} "lightrag-hku[api]"')

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️ 警告: LightRAG需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_pip():
    """检查pip是否可用"""
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip不可用")
            return False
    except Exception as e:
        print(f"❌ pip检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 LightRAG依赖安装器")
    print("=" * 50)
    
    # 检查环境
    if not check_python_version():
        print("请升级Python版本后重试")
        return
    
    if not check_pip():
        print("请确保pip正确安装")
        return
    
    # 确认安装
    print("\n📋 将要安装以下组件:")
    print("- pipmaster (LightRAG依赖)")
    print("- python-dotenv (环境变量)")
    print("- fastapi (Web框架)")
    print("- uvicorn (ASGI服务器)")
    print("- 其他相关依赖...")
    print("- lightrag-hku[api] (LightRAG完整版)")
    
    confirm = input("\n是否继续安装? [Y/n]: ").lower()
    if confirm in ['', 'y', 'yes']:
        install_dependencies()
    else:
        print("安装已取消")

if __name__ == "__main__":
    main()
