#!/usr/bin/env python
"""
无人机知识图谱可视化系统
基于RAG-Anything知识图谱的Web可视化界面
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import networkx as nx
from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder


class DroneKnowledgeVisualizer:
    """无人机知识图谱可视化器"""
    
    def __init__(self, rag_system, situation_awareness):
        self.rag_system = rag_system
        self.situation_awareness = situation_awareness
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'drone_kg_viz_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        self._setup_routes()
        self._setup_socket_events()
    
    def _setup_routes(self):
        """设置Web路由"""
        
        @self.app.route('/')
        def index():
            """主页面"""
            return render_template('drone_dashboard.html')
        
        @self.app.route('/api/knowledge_graph')
        async def get_knowledge_graph():
            """获取知识图谱数据"""
            try:
                graph_data = await self._extract_knowledge_graph()
                return jsonify(graph_data)
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/api/situation_report')
        async def get_situation_report():
            """获取态势报告"""
            try:
                report = await self.situation_awareness.get_situation_report()
                return jsonify(report)
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/api/target_details/<target_id>')
        async def get_target_details(target_id):
            """获取目标详细信息"""
            try:
                target = self.situation_awareness.active_targets.get(target_id)
                if target:
                    return jsonify({
                        "target_id": target.target_id,
                        "position": target.position,
                        "velocity": target.velocity,
                        "threat_level": target.threat_level.value,
                        "drone_type": target.drone_type.value,
                        "confidence": target.confidence,
                        "timestamp": target.timestamp.isoformat(),
                        "radar_features": target.radar_features,
                        "spectrum_features": target.spectrum_features,
                        "image_features": target.image_features
                    })
                else:
                    return jsonify({"error": "Target not found"}), 404
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/api/search')
        async def search_knowledge():
            """知识搜索接口"""
            query = request.args.get('q', '')
            if not query:
                return jsonify({"error": "Query parameter 'q' is required"}), 400
            
            try:
                result = await self.rag_system.aquery(query, mode="hybrid")
                return jsonify({"query": query, "result": result})
            except Exception as e:
                return jsonify({"error": str(e)}), 500
    
    def _setup_socket_events(self):
        """设置WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接事件"""
            print('Client connected')
            emit('status', {'msg': 'Connected to drone monitoring system'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接事件"""
            print('Client disconnected')
        
        @self.socketio.on('request_update')
        async def handle_update_request():
            """处理更新请求"""
            try:
                # 获取最新态势数据
                situation_report = await self.situation_awareness.get_situation_report()
                emit('situation_update', situation_report)
                
                # 获取知识图谱更新
                graph_data = await self._extract_knowledge_graph()
                emit('graph_update', graph_data)
                
            except Exception as e:
                emit('error', {'message': str(e)})
    
    async def _extract_knowledge_graph(self) -> Dict[str, Any]:
        """从RAG系统提取知识图谱数据"""
        
        # 这里需要访问LightRAG的内部图结构
        # 实际实现需要根据LightRAG的具体API调整
        try:
            # 模拟知识图谱数据提取
            nodes = []
            edges = []
            
            # 添加目标节点
            for target_id, target in self.situation_awareness.active_targets.items():
                nodes.append({
                    "id": target_id,
                    "label": f"目标 {target_id}",
                    "type": "target",
                    "threat_level": target.threat_level.value,
                    "drone_type": target.drone_type.value,
                    "confidence": target.confidence,
                    "size": 20 + target.confidence * 30,
                    "color": self._get_threat_color(target.threat_level.value)
                })
            
            # 添加特征节点
            feature_types = ["radar", "spectrum", "image"]
            for feature_type in feature_types:
                nodes.append({
                    "id": f"{feature_type}_features",
                    "label": f"{feature_type.upper()}特征",
                    "type": "feature",
                    "size": 15,
                    "color": "#95a5a6"
                })
                
                # 连接目标和特征
                for target_id in self.situation_awareness.active_targets.keys():
                    edges.append({
                        "from": target_id,
                        "to": f"{feature_type}_features",
                        "label": "具有",
                        "width": 2
                    })
            
            return {
                "nodes": nodes,
                "edges": edges,
                "statistics": {
                    "total_nodes": len(nodes),
                    "total_edges": len(edges),
                    "target_count": len(self.situation_awareness.active_targets)
                }
            }
            
        except Exception as e:
            print(f"Error extracting knowledge graph: {e}")
            return {"nodes": [], "edges": [], "statistics": {}}
    
    def _get_threat_color(self, threat_level: str) -> str:
        """根据威胁等级获取颜色"""
        color_map = {
            "低": "#2ecc71",      # 绿色
            "中": "#f39c12",      # 橙色
            "高": "#e74c3c",      # 红色
            "严重": "#8e44ad"     # 紫色
        }
        return color_map.get(threat_level, "#95a5a6")
    
    def create_3d_situation_plot(self) -> str:
        """创建3D态势图"""
        
        targets = list(self.situation_awareness.active_targets.values())
        if not targets:
            return json.dumps({}, cls=PlotlyJSONEncoder)
        
        # 提取位置数据
        x_coords = [t.position[1] for t in targets]  # 经度
        y_coords = [t.position[0] for t in targets]  # 纬度
        z_coords = [t.position[2] for t in targets]  # 高度
        
        # 提取其他属性
        threat_levels = [t.threat_level.value for t in targets]
        target_ids = [t.target_id for t in targets]
        confidences = [t.confidence for t in targets]
        
        # 创建3D散点图
        fig = go.Figure(data=go.Scatter3d(
            x=x_coords,
            y=y_coords,
            z=z_coords,
            mode='markers+text',
            marker=dict(
                size=[c * 20 + 5 for c in confidences],
                color=[self._get_threat_color(tl) for tl in threat_levels],
                opacity=0.8,
                line=dict(width=2, color='DarkSlateGrey')
            ),
            text=target_ids,
            textposition="top center",
            hovertemplate='<b>%{text}</b><br>' +
                         '经度: %{x}<br>' +
                         '纬度: %{y}<br>' +
                         '高度: %{z}m<br>' +
                         '<extra></extra>'
        ))
        
        fig.update_layout(
            title='无人机目标3D态势图',
            scene=dict(
                xaxis_title='经度',
                yaxis_title='纬度',
                zaxis_title='高度 (m)',
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5)
                )
            ),
            width=800,
            height=600
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_threat_timeline(self) -> str:
        """创建威胁时间线图"""
        
        # 收集历史数据
        timeline_data = []
        for target_id, history in self.situation_awareness.tracking_history.items():
            for record in history[-20:]:  # 最近20个记录
                timeline_data.append({
                    'timestamp': record.timestamp,
                    'target_id': target_id,
                    'threat_level': record.threat_level.value,
                    'confidence': record.confidence
                })
        
        if not timeline_data:
            return json.dumps({}, cls=PlotlyJSONEncoder)
        
        # 按时间排序
        timeline_data.sort(key=lambda x: x['timestamp'])
        
        # 创建时间线图
        fig = px.line(
            timeline_data,
            x='timestamp',
            y='confidence',
            color='target_id',
            title='目标威胁等级时间线',
            labels={
                'timestamp': '时间',
                'confidence': '置信度',
                'target_id': '目标ID'
            }
        )
        
        fig.update_layout(
            width=800,
            height=400,
            xaxis_title='时间',
            yaxis_title='置信度'
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    async def start_real_time_updates(self):
        """启动实时更新"""
        while True:
            try:
                # 获取最新态势报告
                situation_report = await self.situation_awareness.get_situation_report()
                
                # 广播更新
                self.socketio.emit('situation_update', situation_report)
                
                # 如果有高威胁目标，发送警报
                high_threat_targets = [
                    target for target in self.situation_awareness.active_targets.values()
                    if target.threat_level.value in ["高", "严重"]
                ]
                
                if high_threat_targets:
                    alert_data = {
                        "type": "high_threat_alert",
                        "message": f"发现{len(high_threat_targets)}个高威胁目标",
                        "targets": [t.target_id for t in high_threat_targets],
                        "timestamp": datetime.now().isoformat()
                    }
                    self.socketio.emit('alert', alert_data)
                
                # 等待5秒后下次更新
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"Real-time update error: {e}")
                await asyncio.sleep(10)
    
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """运行可视化服务器"""
        print(f"启动无人机知识图谱可视化系统...")
        print(f"访问地址: http://{host}:{port}")
        
        # 启动实时更新任务
        asyncio.create_task(self.start_real_time_updates())
        
        # 运行Flask应用
        self.socketio.run(self.app, host=host, port=port, debug=debug)


# HTML模板（需要保存为templates/drone_dashboard.html）
DASHBOARD_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无人机目标识别态势感知系统</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vis-network/styles/vis-network.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f0f0f0; }
        .dashboard { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .panel h3 { margin-top: 0; color: #333; }
        #knowledge-graph { height: 400px; border: 1px solid #ddd; }
        #situation-plot { height: 400px; }
        .alert { background-color: #ff6b6b; color: white; padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .status { background-color: #4ecdc4; color: white; padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .threat-high { color: #e74c3c; font-weight: bold; }
        .threat-medium { color: #f39c12; font-weight: bold; }
        .threat-low { color: #2ecc71; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🚁 无人机目标识别态势感知系统</h1>
    
    <div id="status-bar"></div>
    <div id="alerts"></div>
    
    <div class="dashboard">
        <div class="panel">
            <h3>📊 态势概览</h3>
            <div id="situation-summary"></div>
            <div id="situation-plot"></div>
        </div>
        
        <div class="panel">
            <h3>🕸️ 知识图谱</h3>
            <div id="knowledge-graph"></div>
        </div>
        
        <div class="panel">
            <h3>🎯 活跃目标</h3>
            <div id="active-targets"></div>
        </div>
        
        <div class="panel">
            <h3>🔍 智能搜索</h3>
            <input type="text" id="search-input" placeholder="输入查询内容..." style="width: 70%; padding: 8px;">
            <button onclick="searchKnowledge()" style="padding: 8px 16px;">搜索</button>
            <div id="search-results" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        const socket = io();
        let knowledgeGraph = null;
        
        // 初始化知识图谱
        function initKnowledgeGraph() {
            const container = document.getElementById('knowledge-graph');
            const options = {
                nodes: { shape: 'dot', size: 16 },
                edges: { width: 2 },
                physics: { enabled: true }
            };
            knowledgeGraph = new vis.Network(container, {nodes: [], edges: []}, options);
        }
        
        // 更新态势信息
        function updateSituationSummary(report) {
            const summary = document.getElementById('situation-summary');
            summary.innerHTML = `
                <p><strong>活跃目标:</strong> ${report.active_targets_count}</p>
                <p><strong>威胁分布:</strong></p>
                <ul>
                    <li class="threat-low">低威胁: ${report.threat_distribution['低'] || 0}</li>
                    <li class="threat-medium">中威胁: ${report.threat_distribution['中'] || 0}</li>
                    <li class="threat-high">高威胁: ${report.threat_distribution['高'] || 0}</li>
                    <li class="threat-high">严重威胁: ${report.threat_distribution['严重'] || 0}</li>
                </ul>
                <p><strong>更新时间:</strong> ${new Date(report.timestamp).toLocaleString()}</p>
            `;
        }
        
        // 搜索知识
        async function searchKnowledge() {
            const query = document.getElementById('search-input').value;
            if (!query) return;
            
            try {
                const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                document.getElementById('search-results').innerHTML = `
                    <h4>搜索结果:</h4>
                    <p>${data.result}</p>
                `;
            } catch (error) {
                console.error('Search error:', error);
            }
        }
        
        // Socket事件处理
        socket.on('connect', function() {
            document.getElementById('status-bar').innerHTML = 
                '<div class="status">✅ 已连接到态势感知系统</div>';
            socket.emit('request_update');
        });
        
        socket.on('situation_update', function(report) {
            updateSituationSummary(report);
        });
        
        socket.on('graph_update', function(graphData) {
            if (knowledgeGraph) {
                knowledgeGraph.setData(graphData);
            }
        });
        
        socket.on('alert', function(alertData) {
            const alertsDiv = document.getElementById('alerts');
            alertsDiv.innerHTML = `
                <div class="alert">
                    🚨 ${alertData.message} - ${new Date(alertData.timestamp).toLocaleString()}
                </div>
            ` + alertsDiv.innerHTML;
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initKnowledgeGraph();
            
            // 定期请求更新
            setInterval(() => {
                socket.emit('request_update');
            }, 10000); // 每10秒更新一次
        });
    </script>
</body>
</html>
"""


# 使用示例
async def main():
    """主函数示例"""
    from raganything import RAGAnything, RAGAnythingConfig
    from drone_situation_awareness import DroneSituationAwareness
    
    # 初始化系统组件
    config = RAGAnythingConfig(working_dir="./drone_knowledge_base")
    rag_system = RAGAnything(config=config)
    situation_awareness = DroneSituationAwareness(rag_system)
    
    # 创建可视化系统
    visualizer = DroneKnowledgeVisualizer(rag_system, situation_awareness)
    
    # 保存HTML模板
    import os
    os.makedirs('templates', exist_ok=True)
    with open('templates/drone_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(DASHBOARD_HTML_TEMPLATE)
    
    # 启动可视化服务器
    visualizer.run(host='0.0.0.0', port=5000, debug=True)


if __name__ == "__main__":
    asyncio.run(main())
