#!/usr/bin/env python
"""
LightRAG服务器启动脚本
解决依赖问题的替代方案
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量
def setup_environment():
    """设置环境变量"""
    env_vars = {
        "HOST": "0.0.0.0",
        "PORT": "9621",
        "WORKERS": "2",
        
        # LLM配置 - DeepSeek
        "LLM_BINDING": "ollama",
        "LLM_MODEL": "deepseek-r1:7b",
        "LLM_BINDING_HOST": "http://localhost:11434",
        "OLLAMA_LLM_NUM_CTX": "32768",
        
        # Embedding配置
        "EMBEDDING_BINDING": "ollama",
        "EMBEDDING_BINDING_HOST": "http://localhost:11434",
        "EMBEDDING_MODEL": "bge-m3:latest",
        "EMBEDDING_DIM": "1024",
        
        # 其他配置
        "SUMMARY_LANGUAGE": "Chinese",
        "LOG_LEVEL": "INFO",
        "MAX_ASYNC": "2",
        "ENABLE_LLM_CACHE": "true",
        "TIMEOUT": "300"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    print("✅ 环境变量设置完成")

def check_dependencies():
    """检查依赖"""
    required_modules = [
        "lightrag",
        "fastapi", 
        "uvicorn",
        "pydantic"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - 已安装")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} - 未安装")
    
    if missing_modules:
        print(f"\n⚠️ 缺少依赖模块: {', '.join(missing_modules)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def start_server_direct():
    """直接启动服务器"""
    try:
        import uvicorn
        from lightrag.api.app import app
        
        print("🚀 启动LightRAG服务器...")
        print("📍 访问地址: http://localhost:9621")
        print("📚 API文档: http://localhost:9621/docs")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=9621,
            workers=1,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请安装缺失的依赖")
        return False
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False

def install_dependencies():
    """自动安装依赖"""
    print("🔧 正在安装缺失的依赖...")
    
    dependencies = [
        "python-dotenv",
        "fastapi",
        "uvicorn[standard]",
        "pydantic",
        "httpx",
        "aiofiles"
    ]
    
    for dep in dependencies:
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {dep} 安装成功")
            else:
                print(f"❌ {dep} 安装失败: {result.stderr}")
        except Exception as e:
            print(f"❌ 安装 {dep} 时出错: {e}")

def main():
    """主函数"""
    print("🚀 LightRAG服务器启动器")
    print("=" * 50)
    
    # 设置环境变量
    setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        choice = input("\n是否自动安装缺失的依赖? (y/n): ")
        if choice.lower() == 'y':
            install_dependencies()
            print("\n依赖安装完成，请重新运行此脚本")
            return
        else:
            print("请手动安装依赖后重新运行")
            return
    
    # 启动服务器
    try:
        start_server_direct()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
