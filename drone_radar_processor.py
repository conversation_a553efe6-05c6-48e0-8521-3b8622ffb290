#!/usr/bin/env python
"""
无人机雷达数据处理器
基于RAG-Anything架构扩展的雷达数据专用处理器
"""

import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from raganything.modalprocessors import GenericModalProcessor


class DroneRadarProcessor(GenericModalProcessor):
    """无人机雷达数据处理器"""
    
    def __init__(self, lightrag, modal_caption_func, **kwargs):
        super().__init__(lightrag, modal_caption_func, **kwargs)
        self.radar_features = [
            "range", "velocity", "azimuth", "elevation", 
            "rcs", "doppler_shift", "signal_strength"
        ]
    
    async def process_multimodal_content(
        self, 
        modal_content: Dict[str, Any], 
        content_type: str, 
        file_path: str, 
        entity_name: str
    ) -> <PERSON><PERSON>[str, Dict[str, Any]]:
        """
        处理雷达数据内容
        
        Args:
            modal_content: 雷达数据内容
                {
                    "radar_data": numpy_array_or_list,
                    "metadata": {
                        "frequency": "X-band",
                        "range_resolution": 0.5,
                        "timestamp": "2024-01-01T12:00:00Z",
                        "target_type": "drone"
                    },
                    "features": {
                        "range": 1500.0,
                        "velocity": 25.0,
                        "azimuth": 45.0,
                        "rcs": -20.5
                    }
                }
        """
        
        # 提取雷达特征
        radar_features = self._extract_radar_features(modal_content)
        
        # 生成雷达数据描述
        radar_description = await self._generate_radar_description(
            modal_content, radar_features
        )
        
        # 创建实体信息
        entity_info = {
            "entity_name": entity_name,
            "entity_type": "RADAR_TARGET",
            "content_type": content_type,
            "radar_features": radar_features,
            "metadata": modal_content.get("metadata", {}),
            "file_path": file_path,
            "description": radar_description
        }
        
        return await self._create_entity_and_chunk(
            radar_description, entity_info, file_path
        )
    
    def _extract_radar_features(self, modal_content: Dict[str, Any]) -> Dict[str, float]:
        """提取雷达特征"""
        features = modal_content.get("features", {})
        
        # 标准化特征
        normalized_features = {}
        for feature in self.radar_features:
            if feature in features:
                normalized_features[feature] = float(features[feature])
        
        # 计算衍生特征
        if "range" in normalized_features and "velocity" in normalized_features:
            normalized_features["threat_level"] = self._calculate_threat_level(
                normalized_features["range"], normalized_features["velocity"]
            )
        
        return normalized_features
    
    def _calculate_threat_level(self, range_val: float, velocity: float) -> float:
        """计算威胁等级"""
        # 简单的威胁等级计算逻辑
        if range_val < 500:  # 500米内
            if velocity > 20:  # 高速接近
                return 0.9
            else:
                return 0.6
        elif range_val < 1000:  # 1000米内
            if velocity > 15:
                return 0.7
            else:
                return 0.4
        else:
            return 0.2
    
    async def _generate_radar_description(
        self, 
        modal_content: Dict[str, Any], 
        features: Dict[str, float]
    ) -> str:
        """生成雷达数据的自然语言描述"""
        
        metadata = modal_content.get("metadata", {})
        
        # 构建描述提示
        prompt = f"""
        分析以下雷达探测数据，生成详细的目标描述：
        
        雷达元数据：
        - 频段: {metadata.get('frequency', '未知')}
        - 距离分辨率: {metadata.get('range_resolution', '未知')}m
        - 探测时间: {metadata.get('timestamp', '未知')}
        - 目标类型: {metadata.get('target_type', '未知')}
        
        目标特征：
        """
        
        for feature, value in features.items():
            if feature == "range":
                prompt += f"- 距离: {value}米\n"
            elif feature == "velocity":
                prompt += f"- 速度: {value}m/s\n"
            elif feature == "azimuth":
                prompt += f"- 方位角: {value}度\n"
            elif feature == "elevation":
                prompt += f"- 俯仰角: {value}度\n"
            elif feature == "rcs":
                prompt += f"- 雷达截面积: {value}dBsm\n"
            elif feature == "threat_level":
                prompt += f"- 威胁等级: {value:.2f}\n"
        
        prompt += """
        请生成一个包含以下内容的综合分析：
        1. 目标的基本特征描述
        2. 运动状态分析
        3. 威胁评估
        4. 建议的应对措施
        """
        
        # 调用LLM生成描述
        description = await self.modal_caption_func(
            prompt=prompt,
            system_prompt="你是一个专业的雷达数据分析专家，专门分析无人机目标的雷达特征。"
        )
        
        return description


class DroneSpectrumProcessor(GenericModalProcessor):
    """无人机频谱数据处理器"""
    
    def __init__(self, lightrag, modal_caption_func, **kwargs):
        super().__init__(lightrag, modal_caption_func, **kwargs)
        self.spectrum_features = [
            "center_frequency", "bandwidth", "power_level", 
            "modulation_type", "signal_pattern", "interference_level"
        ]
    
    async def process_multimodal_content(
        self, 
        modal_content: Dict[str, Any], 
        content_type: str, 
        file_path: str, 
        entity_name: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        处理频谱数据内容
        
        Args:
            modal_content: 频谱数据内容
                {
                    "spectrum_data": frequency_power_array,
                    "metadata": {
                        "frequency_range": [2400, 2500],  # MHz
                        "resolution": 1.0,  # MHz
                        "timestamp": "2024-01-01T12:00:00Z"
                    },
                    "features": {
                        "center_frequency": 2450.0,
                        "bandwidth": 20.0,
                        "power_level": -30.0,
                        "modulation_type": "FHSS"
                    }
                }
        """
        
        # 提取频谱特征
        spectrum_features = self._extract_spectrum_features(modal_content)
        
        # 生成频谱数据描述
        spectrum_description = await self._generate_spectrum_description(
            modal_content, spectrum_features
        )
        
        # 创建实体信息
        entity_info = {
            "entity_name": entity_name,
            "entity_type": "SPECTRUM_SIGNAL",
            "content_type": content_type,
            "spectrum_features": spectrum_features,
            "metadata": modal_content.get("metadata", {}),
            "file_path": file_path,
            "description": spectrum_description
        }
        
        return await self._create_entity_and_chunk(
            spectrum_description, entity_info, file_path
        )
    
    def _extract_spectrum_features(self, modal_content: Dict[str, Any]) -> Dict[str, Any]:
        """提取频谱特征"""
        features = modal_content.get("features", {})
        metadata = modal_content.get("metadata", {})
        
        # 标准化特征
        normalized_features = {}
        for feature in self.spectrum_features:
            if feature in features:
                normalized_features[feature] = features[feature]
        
        # 添加频段分类
        center_freq = features.get("center_frequency", 0)
        normalized_features["frequency_band"] = self._classify_frequency_band(center_freq)
        
        # 信号强度分类
        power_level = features.get("power_level", -100)
        normalized_features["signal_strength"] = self._classify_signal_strength(power_level)
        
        return normalized_features
    
    def _classify_frequency_band(self, frequency: float) -> str:
        """频段分类"""
        if 2400 <= frequency <= 2500:
            return "2.4GHz ISM"
        elif 5150 <= frequency <= 5850:
            return "5GHz WiFi"
        elif 900 <= frequency <= 928:
            return "900MHz ISM"
        else:
            return "Other"
    
    def _classify_signal_strength(self, power_level: float) -> str:
        """信号强度分类"""
        if power_level > -30:
            return "Very Strong"
        elif power_level > -50:
            return "Strong"
        elif power_level > -70:
            return "Medium"
        elif power_level > -90:
            return "Weak"
        else:
            return "Very Weak"
    
    async def _generate_spectrum_description(
        self, 
        modal_content: Dict[str, Any], 
        features: Dict[str, Any]
    ) -> str:
        """生成频谱数据的自然语言描述"""
        
        metadata = modal_content.get("metadata", {})
        
        prompt = f"""
        分析以下频谱监测数据，生成详细的信号特征描述：
        
        频谱元数据：
        - 频率范围: {metadata.get('frequency_range', '未知')} MHz
        - 频率分辨率: {metadata.get('resolution', '未知')} MHz
        - 监测时间: {metadata.get('timestamp', '未知')}
        
        信号特征：
        """
        
        for feature, value in features.items():
            if feature == "center_frequency":
                prompt += f"- 中心频率: {value} MHz\n"
            elif feature == "bandwidth":
                prompt += f"- 带宽: {value} MHz\n"
            elif feature == "power_level":
                prompt += f"- 功率电平: {value} dBm\n"
            elif feature == "modulation_type":
                prompt += f"- 调制类型: {value}\n"
            elif feature == "frequency_band":
                prompt += f"- 频段分类: {value}\n"
            elif feature == "signal_strength":
                prompt += f"- 信号强度: {value}\n"
        
        prompt += """
        请生成包含以下内容的分析报告：
        1. 信号的技术特征分析
        2. 可能的设备类型识别
        3. 通信协议推断
        4. 潜在威胁评估
        """
        
        description = await self.modal_caption_func(
            prompt=prompt,
            system_prompt="你是一个专业的频谱分析专家，专门分析无人机通信信号的频谱特征。"
        )
        
        return description
