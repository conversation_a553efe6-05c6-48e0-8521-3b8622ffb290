#!/usr/bin/env python
"""
无人机目标识别全维知识图谱集成系统
整合RAG-Anything、雷达处理、频谱分析、态势感知和可视化的完整解决方案
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import numpy as np

# 导入自定义模块
from drone_radar_processor import DroneRadarProcessor, DroneSpectrumProcessor
from drone_situation_awareness import DroneSituationAwareness, ThreatLevel, DroneType
from drone_knowledge_visualization import DroneKnowledgeVisualizer

# 导入RAG-Anything
from raganything import RAGAnything, RAGAnythingConfig
from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc


class DroneIntegratedSystem:
    """无人机目标识别全维知识图谱集成系统"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = self._load_config(config_path)
        self.rag_system = None
        self.radar_processor = None
        self.spectrum_processor = None
        self.situation_awareness = None
        self.visualizer = None
        self.logger = self._setup_logging()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载系统配置"""
        default_config = {
            "api_key": "your-api-key",
            "base_url": "your-base-url",
            "working_dir": "./drone_knowledge_base",
            "enable_image_processing": True,
            "enable_table_processing": True,
            "enable_equation_processing": True,
            "visualization_port": 5000,
            "log_level": "INFO"
        }
        
        try:
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
        except Exception as e:
            print(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, self.config.get("log_level", "INFO")),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('drone_system.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    async def initialize(self):
        """初始化系统所有组件"""
        self.logger.info("🚀 开始初始化无人机目标识别全维知识图谱系统...")
        
        try:
            # 1. 初始化RAG-Anything系统
            await self._initialize_rag_system()
            
            # 2. 初始化多模态处理器
            await self._initialize_processors()
            
            # 3. 初始化态势感知系统
            await self._initialize_situation_awareness()
            
            # 4. 初始化可视化系统
            await self._initialize_visualization()
            
            self.logger.info("✅ 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            raise
    
    async def _initialize_rag_system(self):
        """初始化RAG系统"""
        self.logger.info("初始化RAG-Anything系统...")
        
        # 创建配置
        rag_config = RAGAnythingConfig(
            working_dir=self.config["working_dir"],
            enable_image_processing=self.config["enable_image_processing"],
            enable_table_processing=self.config["enable_table_processing"],
            enable_equation_processing=self.config["enable_equation_processing"],
        )
        
        # 定义LLM函数 - 使用Ollama DeepSeek
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            # 如果配置了API密钥，使用OpenAI兼容接口
            if self.config.get("api_key") and self.config["api_key"] != "your-api-key":
                return openai_complete_if_cache(
                    "gpt-4o-mini",
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    api_key=self.config["api_key"],
                    base_url=self.config.get("base_url"),
                    **kwargs,
                )
            else:
                # 使用Ollama DeepSeek模型
                return openai_complete_if_cache(
                    "deepseek-r1:7b",
                    prompt,
                    system_prompt=system_prompt,
                    history_messages=history_messages,
                    base_url="http://localhost:11434/v1",
                    api_key="ollama",  # Ollama不需要真实API密钥
                    **kwargs,
                )
        
        # 定义视觉模型函数
        def vision_model_func(prompt, system_prompt=None, history_messages=[], 
                             image_data=None, messages=None, **kwargs):
            if messages:
                return openai_complete_if_cache(
                    "gpt-4o",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=messages,
                    api_key=self.config["api_key"],
                    base_url=self.config.get("base_url"),
                    **kwargs,
                )
            elif image_data:
                return openai_complete_if_cache(
                    "gpt-4o",
                    "",
                    system_prompt=None,
                    history_messages=[],
                    messages=[
                        {"role": "system", "content": system_prompt} if system_prompt else None,
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                            ],
                        } if image_data else {"role": "user", "content": prompt},
                    ],
                    api_key=self.config["api_key"],
                    base_url=self.config.get("base_url"),
                    **kwargs,
                )
            else:
                return llm_model_func(prompt, system_prompt, history_messages, **kwargs)
        
        # 定义嵌入函数
        embedding_func = EmbeddingFunc(
            embedding_dim=3072,
            max_token_size=8192,
            func=lambda texts: openai_embed(
                texts,
                model="text-embedding-3-large",
                api_key=self.config["api_key"],
                base_url=self.config.get("base_url"),
            ),
        )
        
        # 初始化RAG系统
        self.rag_system = RAGAnything(
            config=rag_config,
            llm_model_func=llm_model_func,
            vision_model_func=vision_model_func,
            embedding_func=embedding_func,
        )
        
        self.logger.info("✅ RAG-Anything系统初始化完成")
    
    async def _initialize_processors(self):
        """初始化多模态处理器"""
        self.logger.info("初始化多模态处理器...")
        
        # 雷达数据处理器
        self.radar_processor = DroneRadarProcessor(
            lightrag=self.rag_system.lightrag,
            modal_caption_func=self.rag_system.llm_model_func
        )
        
        # 频谱数据处理器
        self.spectrum_processor = DroneSpectrumProcessor(
            lightrag=self.rag_system.lightrag,
            modal_caption_func=self.rag_system.llm_model_func
        )
        
        self.logger.info("✅ 多模态处理器初始化完成")
    
    async def _initialize_situation_awareness(self):
        """初始化态势感知系统"""
        self.logger.info("初始化态势感知系统...")
        
        self.situation_awareness = DroneSituationAwareness(self.rag_system)
        
        self.logger.info("✅ 态势感知系统初始化完成")
    
    async def _initialize_visualization(self):
        """初始化可视化系统"""
        self.logger.info("初始化可视化系统...")
        
        self.visualizer = DroneKnowledgeVisualizer(
            self.rag_system, 
            self.situation_awareness
        )
        
        self.logger.info("✅ 可视化系统初始化完成")
    
    async def process_multimodal_data(self, data_package: Dict[str, Any]) -> Dict[str, Any]:
        """处理多模态数据包"""
        self.logger.info(f"处理多模态数据包: {data_package.get('data_id', 'unknown')}")
        
        results = {
            "data_id": data_package.get("data_id"),
            "timestamp": datetime.now().isoformat(),
            "processing_results": {},
            "target_analysis": None,
            "errors": []
        }
        
        try:
            # 1. 处理雷达数据
            if "radar_data" in data_package:
                radar_result = await self.radar_processor.process_multimodal_content(
                    modal_content=data_package["radar_data"],
                    content_type="radar",
                    file_path=data_package.get("source_file", "realtime_data"),
                    entity_name=f"radar_target_{data_package.get('data_id', 'unknown')}"
                )
                results["processing_results"]["radar"] = radar_result
                self.logger.info("✅ 雷达数据处理完成")
            
            # 2. 处理频谱数据
            if "spectrum_data" in data_package:
                spectrum_result = await self.spectrum_processor.process_multimodal_content(
                    modal_content=data_package["spectrum_data"],
                    content_type="spectrum",
                    file_path=data_package.get("source_file", "realtime_data"),
                    entity_name=f"spectrum_signal_{data_package.get('data_id', 'unknown')}"
                )
                results["processing_results"]["spectrum"] = spectrum_result
                self.logger.info("✅ 频谱数据处理完成")
            
            # 3. 处理图像数据（如果有）
            if "image_data" in data_package:
                # 使用RAG-Anything的图像处理能力
                image_content = [{
                    "type": "image",
                    "img_path": data_package["image_data"].get("img_path"),
                    "img_caption": data_package["image_data"].get("img_caption", []),
                    "page_idx": 0
                }]
                
                await self.rag_system.insert_content_list(
                    content_list=image_content,
                    file_path=data_package.get("source_file", "realtime_data")
                )
                results["processing_results"]["image"] = "processed"
                self.logger.info("✅ 图像数据处理完成")
            
            # 4. 综合态势分析
            target_data = {
                "target_id": data_package.get("data_id"),
                "timestamp": datetime.now().isoformat(),
                "position": data_package.get("position", (0, 0, 0)),
                "velocity": data_package.get("velocity", (0, 0, 0)),
                "radar_features": data_package.get("radar_data", {}).get("features", {}),
                "spectrum_features": data_package.get("spectrum_data", {}).get("features", {}),
                "image_features": data_package.get("image_data", {}).get("features", {})
            }
            
            target_analysis = await self.situation_awareness.analyze_multimodal_target(target_data)
            results["target_analysis"] = {
                "target_id": target_analysis.target_id,
                "threat_level": target_analysis.threat_level.value,
                "drone_type": target_analysis.drone_type.value,
                "confidence": target_analysis.confidence,
                "position": target_analysis.position,
                "velocity": target_analysis.velocity
            }
            
            self.logger.info("✅ 目标分析完成")
            
        except Exception as e:
            error_msg = f"数据处理错误: {str(e)}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
        
        return results
    
    async def query_knowledge(self, query: str, mode: str = "hybrid") -> str:
        """查询知识图谱"""
        self.logger.info(f"知识查询: {query}")
        
        try:
            result = await self.rag_system.aquery(query, mode=mode)
            return result
        except Exception as e:
            self.logger.error(f"知识查询失败: {e}")
            return f"查询失败: {str(e)}"
    
    async def get_situation_report(self) -> Dict[str, Any]:
        """获取态势报告"""
        try:
            return await self.situation_awareness.get_situation_report()
        except Exception as e:
            self.logger.error(f"态势报告生成失败: {e}")
            return {"error": str(e)}
    
    def start_visualization_server(self):
        """启动可视化服务器"""
        self.logger.info("启动可视化服务器...")
        
        if self.visualizer:
            self.visualizer.run(
                host='0.0.0.0',
                port=self.config.get("visualization_port", 5000),
                debug=False
            )
        else:
            self.logger.error("可视化系统未初始化")
    
    async def run_demo(self):
        """运行演示"""
        self.logger.info("🎯 开始运行系统演示...")
        
        # 模拟多模态数据
        demo_data = {
            "data_id": "demo_target_001",
            "position": (39.9042, 116.4074, 150),  # 北京坐标，高度150米
            "velocity": (20, 5, 0),  # 向东北方向移动
            "radar_data": {
                "metadata": {
                    "frequency": "X-band",
                    "range_resolution": 0.5,
                    "timestamp": datetime.now().isoformat(),
                    "target_type": "drone"
                },
                "features": {
                    "range": 800.0,
                    "velocity": 22.0,
                    "azimuth": 45.0,
                    "elevation": 15.0,
                    "rcs": -25.5,
                    "signal_strength": -45.0
                }
            },
            "spectrum_data": {
                "metadata": {
                    "frequency_range": [2400, 2500],
                    "resolution": 1.0,
                    "timestamp": datetime.now().isoformat()
                },
                "features": {
                    "center_frequency": 2450.0,
                    "bandwidth": 20.0,
                    "power_level": -35.0,
                    "modulation_type": "FHSS"
                }
            },
            "source_file": "demo_realtime_data"
        }
        
        # 处理演示数据
        processing_result = await self.process_multimodal_data(demo_data)
        self.logger.info(f"演示数据处理结果: {json.dumps(processing_result, ensure_ascii=False, indent=2)}")
        
        # 查询知识
        query_result = await self.query_knowledge("分析当前检测到的无人机目标特征和威胁等级")
        self.logger.info(f"知识查询结果: {query_result}")
        
        # 获取态势报告
        situation_report = await self.get_situation_report()
        self.logger.info(f"态势报告: {json.dumps(situation_report, ensure_ascii=False, indent=2)}")
        
        self.logger.info("✅ 演示完成")


# 配置文件模板
CONFIG_TEMPLATE = {
    "api_key": "your-openai-api-key",
    "base_url": "https://api.openai.com/v1",
    "working_dir": "./drone_knowledge_base",
    "enable_image_processing": True,
    "enable_table_processing": True,
    "enable_equation_processing": True,
    "visualization_port": 5000,
    "log_level": "INFO"
}


async def main():
    """主函数"""
    print("🚁 无人机目标识别全维知识图谱系统")
    print("=" * 50)
    
    # 创建配置文件（如果不存在）
    config_path = "config.json"
    if not Path(config_path).exists():
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(CONFIG_TEMPLATE, f, ensure_ascii=False, indent=2)
        print(f"📝 已创建配置文件: {config_path}")
        print("请编辑配置文件中的API密钥等信息后重新运行")
        return
    
    # 初始化系统
    system = DroneIntegratedSystem(config_path)
    
    try:
        # 初始化所有组件
        await system.initialize()
        
        # 运行演示
        await system.run_demo()
        
        # 启动可视化服务器（这会阻塞主线程）
        print("\n🌐 启动Web可视化界面...")
        print("访问 http://localhost:5000 查看态势感知界面")
        system.start_visualization_server()
        
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 系统运行错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
