#!/usr/bin/env python
"""
无人机目标识别全维知识图谱集成系统（修复版）
基于LightRAG的简化集成方案
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 导入LightRAG
from lightrag import LightRAG, QueryParam
from lightrag.llm.ollama import ollama_model_complete, ollama_embed
from lightrag.utils import EmbeddingFunc


class DroneIntegratedSystem:
    """修复版无人机集成系统"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = self._load_config(config_path)
        self.rag_system = None
        self.logger = self._setup_logging()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载系统配置"""
        default_config = {
            "working_dir": "./drone_knowledge_base",
            "llm_model": "deepseek-r1:7b",
            "embedding_model": "bge-m3:latest",
            "ollama_host": "http://localhost:11434",
            "log_level": "INFO"
        }
        
        try:
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
        except Exception as e:
            print(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, self.config.get("log_level", "INFO")),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    async def initialize(self):
        """初始化系统"""
        self.logger.info("🚀 开始初始化无人机系统...")
        
        try:
            # 初始化RAG系统
            await self._initialize_rag_system()
            self.logger.info("✅ 系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            raise
    
    async def _initialize_rag_system(self):
        """初始化RAG系统"""
        self.logger.info("初始化LightRAG系统...")
        
        # 工作目录
        working_dir = self.config["working_dir"]
        if not Path(working_dir).exists():
            Path(working_dir).mkdir(parents=True, exist_ok=True)
        
        # LLM函数
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return ollama_model_complete(
                prompt,
                self.config["llm_model"],
                system_prompt=system_prompt,
                history_messages=history_messages,
                host=self.config["ollama_host"],
                **kwargs
            )
        
        # 嵌入函数
        embedding_func = EmbeddingFunc(
            embedding_dim=1024,
            max_token_size=8192,
            func=lambda texts: ollama_embed(
                texts,
                embed_model=self.config["embedding_model"],
                host=self.config["ollama_host"]
            )
        )
        
        # 创建RAG实例
        self.rag_system = LightRAG(
            working_dir=working_dir,
            llm_model_func=llm_model_func,
            embedding_func=embedding_func
        )
        
        self.logger.info("✅ LightRAG系统初始化完成")
    
    async def process_multimodal_data(self, data_package: Dict[str, Any]) -> Dict[str, Any]:
        """处理多模态数据包"""
        self.logger.info(f"处理多模态数据包: {data_package.get('data_id', 'unknown')}")
        
        results = {
            "data_id": data_package.get("data_id"),
            "timestamp": datetime.now().isoformat(),
            "processing_results": {},
            "target_analysis": None,
            "errors": []
        }
        
        try:
            # 构建数据描述
            description = self._build_data_description(data_package)
            
            # 插入到知识图谱
            await self.rag_system.ainsert(description)
            
            # 分析目标
            analysis = await self._analyze_target(data_package, description)
            results["target_analysis"] = analysis
            
            self.logger.info("✅ 数据处理完成")
            
        except Exception as e:
            error_msg = f"数据处理错误: {str(e)}"
            self.logger.error(error_msg)
            results["errors"].append(error_msg)
        
        return results
    
    def _build_data_description(self, data_package: Dict[str, Any]) -> str:
        """构建数据描述"""
        description_parts = []
        
        # 基本信息
        data_id = data_package.get("data_id", "unknown")
        timestamp = data_package.get("timestamp", datetime.now().isoformat())
        description_parts.append(f"无人机目标 {data_id} 探测数据 ({timestamp}):")
        
        # 位置信息
        if "position" in data_package:
            pos = data_package["position"]
            description_parts.append(f"位置: 纬度{pos[0]}, 经度{pos[1]}, 高度{pos[2]}米")
        
        # 速度信息
        if "velocity" in data_package:
            vel = data_package["velocity"]
            description_parts.append(f"速度: 东向{vel[0]}m/s, 北向{vel[1]}m/s, 垂直{vel[2]}m/s")
        
        # 雷达数据
        if "radar_data" in data_package:
            radar = data_package["radar_data"]
            features = radar.get("features", {})
            description_parts.append("雷达特征:")
            for key, value in features.items():
                description_parts.append(f"  {key}: {value}")
        
        # 频谱数据
        if "spectrum_data" in data_package:
            spectrum = data_package["spectrum_data"]
            features = spectrum.get("features", {})
            description_parts.append("频谱特征:")
            for key, value in features.items():
                description_parts.append(f"  {key}: {value}")
        
        return "\n".join(description_parts)
    
    async def _analyze_target(self, data_package: Dict[str, Any], description: str) -> Dict[str, Any]:
        """分析目标"""
        
        analysis_query = f"""
        分析以下无人机目标数据：
        
        {description}
        
        请提供：
        1. 目标特征分析
        2. 威胁等级评估
        3. 目标类型判断
        4. 应对建议
        """
        
        try:
            result = await self.rag_system.aquery(
                analysis_query,
                param=QueryParam(mode="hybrid")
            )
            
            return {
                "target_id": data_package.get("data_id"),
                "threat_level": "中等",  # 简化处理
                "drone_type": "未知",
                "confidence": 0.8,
                "analysis_result": result,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"目标分析失败: {e}")
            return {
                "target_id": data_package.get("data_id"),
                "error": str(e)
            }
    
    async def query_knowledge(self, query: str, mode: str = "hybrid") -> str:
        """查询知识图谱"""
        self.logger.info(f"知识查询: {query}")
        
        try:
            result = await self.rag_system.aquery(query, param=QueryParam(mode=mode))
            return result
        except Exception as e:
            self.logger.error(f"知识查询失败: {e}")
            return f"查询失败: {str(e)}"
    
    async def get_situation_report(self) -> Dict[str, Any]:
        """获取态势报告"""
        return {
            "system_status": "运行正常",
            "active_targets_count": 0,
            "threat_distribution": {"低": 0, "中": 0, "高": 0, "严重": 0},
            "timestamp": datetime.now().isoformat()
        }
    
    async def run_demo(self):
        """运行演示"""
        self.logger.info("🎯 开始运行系统演示...")
        
        # 模拟数据
        demo_data = {
            "data_id": "demo_target_001",
            "timestamp": datetime.now().isoformat(),
            "position": (39.9042, 116.4074, 150),
            "velocity": (20, 5, 0),
            "radar_data": {
                "features": {
                    "range": 800.0,
                    "velocity": 22.0,
                    "azimuth": 45.0,
                    "rcs": -25.5
                }
            },
            "spectrum_data": {
                "features": {
                    "center_frequency": 2450.0,
                    "bandwidth": 20.0,
                    "power_level": -35.0
                }
            }
        }
        
        # 处理数据
        result = await self.process_multimodal_data(demo_data)
        self.logger.info(f"处理结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 查询知识
        query_result = await self.query_knowledge("分析无人机目标特征")
        self.logger.info(f"查询结果: {query_result}")
        
        self.logger.info("✅ 演示完成")


async def main():
    """主函数"""
    print("🚁 无人机目标识别系统（修复版）")
    print("=" * 50)
    
    system = DroneIntegratedSystem()
    
    try:
        await system.initialize()
        await system.run_demo()
        
        print("\n🎉 系统运行成功！")
        
    except Exception as e:
        print(f"❌ 系统运行错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
