@echo off
chcp 65001 >nul
echo 🔧 修复Python默认版本脚本
echo ================================================

echo 📍 当前状态检查:
echo Python命令版本:
python --version
echo.
echo Py启动器版本:
py --version
echo.

echo 🔍 检查PATH中的Python路径:
where python
echo.

echo ⚠️  如果上面显示的python路径不是Python 3.12，需要修改PATH环境变量
echo.
echo 📋 手动修改步骤:
echo 1. 按Win+R，输入: sysdm.cpl
echo 2. 点击"高级"选项卡 → "环境变量"
echo 3. 在"系统变量"中找到"Path"，双击编辑
echo 4. 将以下路径移到最前面:
echo    E:\python3.12\
echo    E:\python3.12\Scripts\
echo 5. 确定保存，重启命令提示符
echo.

echo 🚀 或者使用临时解决方案:
echo 在当前会话中临时设置Python 3.12为默认:
set "PATH=E:\python3.12;E:\python3.12\Scripts;%PATH%"

echo.
echo 📍 临时修改后的版本:
python --version
echo.

echo 💡 现在可以尝试安装和运行LightRAG:
echo pip install "lightrag-hku[api]"
echo lightrag-server
echo.

echo ⚠️  注意: 这个修改只在当前窗口有效
echo 要永久修改，请按照上面的手动步骤操作
echo.

pause
