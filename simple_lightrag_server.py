#!/usr/bin/env python
"""
简化的LightRAG服务器
直接使用LightRAG核心功能，不依赖API模块
"""

import os
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# 设置环境变量
os.environ.update({
    "LLM_BINDING": "ollama",
    "LLM_MODEL": "deepseek-r1:7b",
    "LLM_BINDING_HOST": "http://localhost:11434",
    "OLLAMA_LLM_NUM_CTX": "32768",
    "EMBEDDING_BINDING": "ollama",
    "EMBEDDING_BINDING_HOST": "http://localhost:11434",
    "EMBEDDING_MODEL": "bge-m3:latest",
    "EMBEDDING_DIM": "1024",
})

try:
    from fastapi import FastAPI, HTTPException, UploadFile, File
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
    
    # LightRAG导入
    from lightrag import LightRAG, QueryParam
    from lightrag.llm.ollama import ollama_model_complete, ollama_embed
    from lightrag.utils import EmbeddingFunc
    
    print("✅ 所有依赖导入成功")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请安装缺失的依赖")
    exit(1)

# 数据模型
class QueryRequest(BaseModel):
    query: str
    mode: str = "hybrid"

class DocumentRequest(BaseModel):
    content: str
    description: str = ""

class LightRAGServer:
    """简化的LightRAG服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="LightRAG Server", version="1.0.0")
        self.rag = None
        self.setup_cors()
        self.setup_routes()
    
    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    async def initialize_rag(self):
        """初始化RAG系统"""
        if self.rag is not None:
            return
        
        print("🚀 初始化LightRAG系统...")
        
        # 工作目录
        working_dir = "./lightrag_cache"
        if not os.path.exists(working_dir):
            os.makedirs(working_dir)
        
        # LLM函数
        def llm_model_func(prompt, system_prompt=None, history_messages=[], **kwargs):
            return ollama_model_complete(
                prompt,
                "deepseek-r1:7b",
                system_prompt=system_prompt,
                history_messages=history_messages,
                host="http://localhost:11434",
                **kwargs
            )
        
        # 嵌入函数
        embedding_func = EmbeddingFunc(
            embedding_dim=1024,
            max_token_size=8192,
            func=lambda texts: ollama_embed(
                texts,
                embed_model="bge-m3:latest",
                host="http://localhost:11434"
            )
        )
        
        # 创建RAG实例
        self.rag = LightRAG(
            working_dir=working_dir,
            llm_model_func=llm_model_func,
            embedding_func=embedding_func
        )
        
        print("✅ LightRAG系统初始化完成")
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def root():
            """主页"""
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <title>LightRAG Server</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    h1 { color: #333; text-align: center; }
                    .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                    textarea { width: 100%; height: 100px; margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
                    input[type="text"] { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
                    button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
                    button:hover { background-color: #0056b3; }
                    .result { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 4px; white-space: pre-wrap; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🚀 LightRAG 无人机知识图谱系统</h1>
                    
                    <div class="section">
                        <h3>📄 上传文档</h3>
                        <textarea id="docContent" placeholder="在这里输入文档内容..."></textarea>
                        <input type="text" id="docDesc" placeholder="文档描述（可选）">
                        <button onclick="uploadDocument()">上传文档</button>
                    </div>
                    
                    <div class="section">
                        <h3>🔍 智能查询</h3>
                        <input type="text" id="queryText" placeholder="输入你的问题...">
                        <select id="queryMode">
                            <option value="hybrid">混合模式</option>
                            <option value="local">本地模式</option>
                            <option value="global">全局模式</option>
                            <option value="naive">简单模式</option>
                        </select>
                        <button onclick="performQuery()">查询</button>
                    </div>
                    
                    <div class="section">
                        <h3>📊 系统状态</h3>
                        <button onclick="checkHealth()">检查状态</button>
                        <button onclick="getStats()">获取统计</button>
                    </div>
                    
                    <div id="result" class="result" style="display:none;"></div>
                </div>
                
                <script>
                    function showResult(content) {
                        document.getElementById('result').style.display = 'block';
                        document.getElementById('result').textContent = content;
                    }
                    
                    async function uploadDocument() {
                        const content = document.getElementById('docContent').value;
                        const description = document.getElementById('docDesc').value;
                        
                        if (!content.trim()) {
                            alert('请输入文档内容');
                            return;
                        }
                        
                        try {
                            const response = await fetch('/documents/text', {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'},
                                body: JSON.stringify({content: content, description: description})
                            });
                            
                            const result = await response.json();
                            showResult('文档上传结果: ' + JSON.stringify(result, null, 2));
                        } catch (error) {
                            showResult('上传失败: ' + error.message);
                        }
                    }
                    
                    async function performQuery() {
                        const query = document.getElementById('queryText').value;
                        const mode = document.getElementById('queryMode').value;
                        
                        if (!query.trim()) {
                            alert('请输入查询内容');
                            return;
                        }
                        
                        try {
                            const response = await fetch('/query', {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'},
                                body: JSON.stringify({query: query, mode: mode})
                            });
                            
                            const result = await response.json();
                            showResult('查询结果: ' + result.answer);
                        } catch (error) {
                            showResult('查询失败: ' + error.message);
                        }
                    }
                    
                    async function checkHealth() {
                        try {
                            const response = await fetch('/health');
                            const result = await response.json();
                            showResult('系统状态: ' + JSON.stringify(result, null, 2));
                        } catch (error) {
                            showResult('状态检查失败: ' + error.message);
                        }
                    }
                    
                    async function getStats() {
                        try {
                            const response = await fetch('/stats');
                            const result = await response.json();
                            showResult('系统统计: ' + JSON.stringify(result, null, 2));
                        } catch (error) {
                            showResult('获取统计失败: ' + error.message);
                        }
                    }
                </script>
            </body>
            </html>
            """
        
        @self.app.get("/health")
        async def health():
            """健康检查"""
            await self.initialize_rag()
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "rag_initialized": self.rag is not None
            }
        
        @self.app.get("/stats")
        async def stats():
            """获取统计信息"""
            await self.initialize_rag()
            return {
                "working_dir": "./lightrag_cache",
                "model": "deepseek-r1:7b",
                "embedding_model": "bge-m3:latest",
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/documents/text")
        async def upload_text_document(request: DocumentRequest):
            """上传文本文档"""
            await self.initialize_rag()
            
            try:
                # 插入文档
                await self.rag.ainsert(request.content)
                
                return {
                    "status": "success",
                    "message": "文档上传成功",
                    "description": request.description,
                    "content_length": len(request.content),
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"文档上传失败: {str(e)}")
        
        @self.app.post("/query")
        async def query(request: QueryRequest):
            """查询"""
            await self.initialize_rag()
            
            try:
                # 执行查询
                result = await self.rag.aquery(
                    request.query,
                    param=QueryParam(mode=request.mode)
                )
                
                return {
                    "query": request.query,
                    "mode": request.mode,
                    "answer": result,
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    def run(self, host="0.0.0.0", port=9621):
        """运行服务器"""
        print(f"🚀 启动LightRAG服务器...")
        print(f"📍 访问地址: http://{host}:{port}")
        print(f"📚 Web界面: http://{host}:{port}")
        
        uvicorn.run(self.app, host=host, port=port)

def main():
    """主函数"""
    server = LightRAGServer()
    server.run()

if __name__ == "__main__":
    main()
